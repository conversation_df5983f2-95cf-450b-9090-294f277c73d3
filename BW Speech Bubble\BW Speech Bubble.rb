#-------------------------------------------------------------------------------
# BW Speech Bubbles for v21
# Updated by NoNonever
#-------------------------------------------------------------------------------
# To use, call pbCallBub(type, eventID)
#
# Where type is either 1 or 2:
# 1 - floating bubble
# 2 - speech bubble with arrow
#-------------------------------------------------------------------------------

#-------------------------------------------------------------------------------
# Class modifiers
#-------------------------------------------------------------------------------

class Game_Temp
  attr_accessor :speechbubble_bubble
  attr_accessor :speechbubble_vp
  attr_accessor :speechbubble_arrow
  attr_accessor :speechbubble_outofrange
  attr_accessor :speechbubble_talking
  attr_accessor :speechbubble_arrow_direction
  attr_accessor :speechbubble_msgwindow
end

module MessageConfig
  BUBBLETEXTBASE  = Color.new(248,248,248)
  BUBBLETEXTSHADOW= Color.new(72,80,88)

  # Get the appropriate bubble skin based on the current theme setting
  def self.pbGetBubbleSkin
    if $PokemonSystem && $PokemonSystem.bubbletheme == 1
      return "Graphics/Plugins/Speech Bubble/bubbleskindark"
    else
      return "Graphics/Plugins/Speech Bubble/bubbleskinlight"
    end
  end

  # Get the appropriate arrow graphic based on theme and direction
  def self.pbGetBubbleArrow(direction)
    theme_suffix = ($PokemonSystem && $PokemonSystem.bubbletheme == 1) ? "Dark" : "Light"
    case direction
    when :up
      return "Graphics/Plugins/Speech Bubble/Arrow_Up_#{theme_suffix}"
    when :down
      return "Graphics/Plugins/Speech Bubble/Arrow_Down_#{theme_suffix}"
    else
      return "Graphics/Plugins/Speech Bubble/Arrow_Down_#{theme_suffix}" # Default to down
    end
  end
end

#-------------------------------------------------------------------------------
# Override pause arrow for speech bubbles
#-------------------------------------------------------------------------------

class Window_AdvancedTextPokemon
  alias speechbubble_allocPause allocPause unless defined?(speechbubble_allocPause)

  def allocPause
    return if @pausesprite
    # Use custom arrow for speech bubbles, default arrow otherwise
    if $game_temp.speechbubble_bubble && $game_temp.speechbubble_bubble > 0
      @pausesprite = AnimatedSprite.create("Graphics/Plugins/Speech Bubble/arrowcontinue", 4, 3)
    else
      @pausesprite = AnimatedSprite.create("Graphics/UI/pause_arrow", 4, 3)
    end
    @pausesprite.z       = 100000
    @pausesprite.visible = false
  end
end

#-------------------------------------------------------------------------------
# Function modifiers
#-------------------------------------------------------------------------------

class Window_AdvancedTextPokemon
  def text=(value)
    if value != nil && value != "" && $game_temp.speechbubble_bubble && $game_temp.speechbubble_bubble > 0
      if $game_temp.speechbubble_bubble == 1
        $game_temp.speechbubble_bubble = 0
        resizeToFit2(value,400,100)
        # Use player if speaking, otherwise the event
        if $game_temp.speechbubble_talking == -1
          speaker_x = $game_player.screen_x
          speaker_y = $game_player.screen_y
        else
          speaker = $game_map.events[$game_temp.speechbubble_talking]
          speaker_x = speaker.screen_x
          speaker_y = speaker.screen_y
        end
        @x = speaker_x
        @y = speaker_y - (32 + @height)
            
        if @y > (Graphics.height-@height-2)
          @y = (Graphics.height-@height)
        elsif @y < 2
          @y = 2
        end
        if @x > (Graphics.width-@width-2)
          @x = speaker_x - @width
        elsif @x < 2
          @x = 2
        end
      else
        $game_temp.speechbubble_bubble = 0
      end
    end
    setText(value)
  end
end 

def pbRepositionMessageWindow(msgwindow, linecount=2)
  msgwindow.height=32*linecount+msgwindow.borderY
  msgwindow.y=(Graphics.height)-(msgwindow.height)
  if $game_temp && $game_temp.in_battle && !$scene.respond_to?("update_basic")
    msgwindow.y=0
  elsif $game_system && $game_system.respond_to?("message_position")
    case $game_system.message_position
    when 0  # up
      msgwindow.y=0
    when 1  # middle
      msgwindow.y=(Graphics.height/2)-(msgwindow.height/2)
    when 2
      if $game_temp.speechbubble_bubble==1
       msgwindow.setSkin(MessageConfig.pbGetBubbleSkin)
       msgwindow.height = 100
       msgwindow.width = 400
     elsif $game_temp.speechbubble_bubble==2
       msgwindow.setSkin(MessageConfig.pbGetBubbleSkin)
       msgwindow.height = 96
       msgwindow.width = Graphics.width * 0.85

       # Determine speaker position for bubble placement
       if $game_temp.speechbubble_talking == -1
         speaker_y = $game_player.screen_y
       else
         speaker = $game_map.events[$game_temp.speechbubble_talking]
         speaker_y = speaker.screen_y
       end

       # Position bubble based on speaker location
       screen_center_y = Graphics.height / 2
       # Center the bubble horizontally
       msgwindow.x = (Graphics.width - msgwindow.width) / 2

       if speaker_y < screen_center_y
          # Speaker in upper half - show bubble above (arrow points down)
         msgwindow.y = (Graphics.height - msgwindow.height) - 272
         $game_temp.speechbubble_outofrange = false
       else
         # Speaker in lower half - show bubble below (arrow points up)
         msgwindow.y = 250
         $game_temp.speechbubble_outofrange = true
       end
      else
        msgwindow.height = 102
        msgwindow.y = Graphics.height - msgwindow.height - 6
      end
    end
  end
  if $game_system && $game_system.respond_to?("message_frame")
    if $game_system.message_frame != 0
      msgwindow.opacity = 0
    end
  end
  if $game_message
    case $game_message.background
      when 1  # dim
        msgwindow.opacity=0
      when 2  # transparent
        msgwindow.opacity=0
    end
  end
end
 
def pbUpdateSpeechBubbleArrow
  return if !$game_temp.speechbubble_arrow || !$game_temp.speechbubble_talking

  # Get current speaker position
  if $game_temp.speechbubble_talking == -1
    speaker_x = $game_player.screen_x
    speaker_y = $game_player.screen_y
  else
    speaker = $game_map.events[$game_temp.speechbubble_talking]
    return if !speaker
    speaker_x = speaker.screen_x
    speaker_y = speaker.screen_y
  end

  # Update arrow position based on current direction
  if $game_temp.speechbubble_arrow_direction == :down
    $game_temp.speechbubble_arrow.x = speaker_x - 8
    $game_temp.speechbubble_arrow.y = speaker_y - 64
  else
    $game_temp.speechbubble_arrow.x = speaker_x - 8
    $game_temp.speechbubble_arrow.y = speaker_y + 2
  end
end

def pbUpdateFloatingBubble
  return if !$game_temp.speechbubble_msgwindow || !$game_temp.speechbubble_talking

  msgwindow = $game_temp.speechbubble_msgwindow

  # Get current speaker position
  if $game_temp.speechbubble_talking == -1
    speaker_x = $game_player.screen_x
    speaker_y = $game_player.screen_y
  else
    speaker = $game_map.events[$game_temp.speechbubble_talking]
    return if !speaker
    speaker_x = speaker.screen_x
    speaker_y = speaker.screen_y
  end

  # Update floating bubble position
  msgwindow.x = speaker_x
  msgwindow.y = speaker_y - (32 + msgwindow.height)

  # Keep bubble on screen
  if msgwindow.y > (Graphics.height - msgwindow.height - 2)
    msgwindow.y = (Graphics.height - msgwindow.height)
  elsif msgwindow.y < 2
    msgwindow.y = 2
  end
  if msgwindow.x > (Graphics.width - msgwindow.width - 2)
    msgwindow.x = speaker_x - msgwindow.width
  elsif msgwindow.x < 2
    msgwindow.x = 2
  end
end

def pbCreateMessageWindow(viewport = nil, skin = nil)
  arrow = nil
  if $game_temp.speechbubble_bubble == 2 && ( $game_temp.speechbubble_talking == -1 || $game_map.events[$game_temp.speechbubble_talking] != nil)
    # Determine speaker x and y (player or event)
    if $game_temp.speechbubble_talking == -1
      speaker_x = $game_player.screen_x
      speaker_y = $game_player.screen_y
    else
      speaker = $game_map.events[$game_temp.speechbubble_talking]
      speaker_x = speaker.screen_x
      speaker_y = speaker.screen_y
    end

    # Determine if NPC is closer to top or bottom of screen
    screen_center_y = Graphics.height / 2
    is_npc_in_upper_half = speaker_y < screen_center_y

    if is_npc_in_upper_half
      # NPC is in upper half - show bubble below with arrow pointing down
      $game_temp.speechbubble_vp = Viewport.new(0, 0, Graphics.width, Graphics.height)
      $game_temp.speechbubble_vp.z = 999999
      arrow = Sprite.new($game_temp.speechbubble_vp)
      arrow.x = speaker_x - 8  # Center arrow on speaker
      arrow.y = speaker_y - 64  # Position arrow above speaker
      arrow.z = 999999
      arrow.bitmap = RPG::Cache.load_bitmap("", MessageConfig.pbGetBubbleArrow(:down))
      $game_temp.speechbubble_outofrange = false
      $game_temp.speechbubble_arrow_direction = :down
    else
      # NPC is in lower half - show bubble above with arrow pointing up
      $game_temp.speechbubble_vp = Viewport.new(0, 0, Graphics.width, Graphics.height)
      $game_temp.speechbubble_vp.z = 999999
      arrow = Sprite.new($game_temp.speechbubble_vp)
      arrow.x = speaker_x - 8  # Center arrow on speaker
      arrow.y = speaker_y + 2 # Position arrow below speaker
      arrow.z = 999999
      arrow.bitmap = RPG::Cache.load_bitmap("", MessageConfig.pbGetBubbleArrow(:up))
      $game_temp.speechbubble_outofrange = true
      $game_temp.speechbubble_arrow_direction = :up
    end
  end
  $game_temp.speechbubble_arrow = arrow
  msgwindow=Window_AdvancedTextPokemon.new("")
  if !viewport
    msgwindow.z=99999
  else
    msgwindow.viewport=viewport
  end
  msgwindow.visible=true
  msgwindow.letterbyletter=true
  msgwindow.back_opacity=MessageConfig::WINDOW_OPACITY
  pbBottomLeftLines(msgwindow,2)
  $game_temp.message_window_showing=true if $game_temp
  $game_message.visible=true if $game_message
  skin=MessageConfig.pbGetSpeechFrame() if !skin
  msgwindow.setSkin(skin)
  # Store reference to message window for floating bubble updates
  $game_temp.speechbubble_msgwindow = msgwindow if $game_temp.speechbubble_bubble == 1
  return msgwindow
end

def pbDisposeMessageWindow(msgwindow)
  $game_temp.message_window_showing=false if $game_temp
  $game_message.visible=false if $game_message
  msgwindow.dispose
  $game_temp.speechbubble_arrow.dispose if $game_temp.speechbubble_arrow
  $game_temp.speechbubble_vp.dispose if $game_temp.speechbubble_vp
  $game_temp.speechbubble_arrow = nil
  $game_temp.speechbubble_vp = nil
  $game_temp.speechbubble_arrow_direction = nil
  $game_temp.speechbubble_msgwindow = nil
end

def pbCallBub(type, value)
  if value == -1
    $game_temp.speechbubble_talking = -1
  else
    $game_temp.speechbubble_talking = get_character(value).id
  end
  $game_temp.speechbubble_bubble = type
end

#===============================================================================
# Override pbUpdateSceneMap to include speech bubble arrow updates
#===============================================================================
alias speechbubble_pbUpdateSceneMap pbUpdateSceneMap unless defined?(speechbubble_pbUpdateSceneMap)

def pbUpdateSceneMap
  speechbubble_pbUpdateSceneMap
  pbUpdateSpeechBubbleArrow
  pbUpdateFloatingBubble
end